# 驱动工具类
import time

from selenium import webdriver

@classmethod
class DriverUtils:
    # 私有属性
    __driver = None
    # 获取浏览器驱动对象
    def get_driver(cls):
        if cls.__driver is None:
            cls.driver=webdriver.Chrome()
            cls.driver.maximize_window()
            cls.driver.implicitly_wait(10)
        return cls.driver

    @classmethod
    # 关闭浏览器驱动对象
    def quit_driver(cls):
        if cls.__driver is not None:
            time.sleep(3)
            cls.__driver.quit()
